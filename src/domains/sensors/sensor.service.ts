import { Injectable, NotFoundException, BadRequestException, Inject } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { Sensor } from './sensor.interface';
import { CreateSensorDto } from './dto/create-sensor.dto';
import { UpdateSensorDto } from './dto/update-sensor.dto';
import Constants from 'src/common/constants';
import { AssignmentDto } from './dto/assign-sensor.dto';
import { SensorStatsDto } from './dto/sensor-stats.dto';
import { BulkAssignmentDto } from './dto/bulk-assign-sensor.dto';
import { LoggingService } from '../logging/logging.service';
import { LogActionEnum } from 'src/common/enums/LogActionEnum';
import { LogEntityEnum } from 'src/common/enums/LogEntityEnum';

@Injectable()
export class SensorService {
  private static readonly CACHE_TTL_SENSORS = 300; // 5 minutes
  private static readonly CACHE_TTL_SENSOR_DETAIL = 600; // 10 minutes

  constructor(
    @InjectModel(Constants.sensorProfile) private sensorModel: Model<Sensor>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private readonly loggingService: LoggingService,
  ) {}

  async create(sensorData: CreateSensorDto, userId: string): Promise<Sensor> {
    // Validate if NODE_ID already exists
    const existingSensor = await this.sensorModel.findOne({
      NODE_ID: sensorData.NODE_ID,
      isDeleted: false
    }).exec();

    if (existingSensor) {
      throw new BadRequestException(`Sensor with NODE_ID ${sensorData.NODE_ID} already exists`);
    }

    // Create new sensor
    const newSensor = new this.sensorModel({
      ...sensorData,
      TIME_STAMP: sensorData.TIME_STAMP || new Date(),
      first_seen: sensorData.first_seen || new Date(),
      createdBy: new Types.ObjectId(userId),
      updatedBy: new Types.ObjectId(userId),
    });

    const savedSensor = await newSensor.save();

    // Clear cache - we'll clear specific cache patterns
    if (process.env.ENABLE_CACHE === 'true') {
      // Clear common cache patterns that might be affected
      await this.cacheManager.del(`sensors:all:${sensorData.assignment?.org_id || 'all'}:1:10`);
      await this.cacheManager.del(`sensors:org:${sensorData.assignment?.org_id}`);
    }

    return savedSensor;
  }

  async bulkAssignSensorToOrg(assignmentDto: BulkAssignmentDto[], updatedBy: string, orgId: string): Promise<null> {
    const promises = assignmentDto.map((assignment) => {
      return this.update(assignment.node_id, { 'assignment': {org_id: new Types.ObjectId(assignment.org_id), auth0_id: assignment.auth0_id, assigned_at: new Date(), notes: assignment.notes }}, updatedBy).then(async sensor => {
        return this.loggingService.createLog(
          LogActionEnum.ASSIGN,
          LogEntityEnum.SENSOR,
          sensor._id.toString(),
          updatedBy,
          {
            assigned_org: assignment.org_id,
            notes: assignment.notes,
          },
          orgId
        );
      });
    });

    await Promise.all(promises);

    return;
  }

  async assignSensorToOrg(assignmentDto: AssignmentDto, node_id: string, updatedBy: string, orgId: string): Promise<AssignmentDto> {
    const updatedSensor = await this.update(node_id, { 'assignment': {org_id: new Types.ObjectId(assignmentDto.org_id), auth0_id: assignmentDto.auth0_id, assigned_at: new Date(), notes: assignmentDto.notes }}, updatedBy);
    
    await this.loggingService.createLog(
      LogActionEnum.ASSIGN,
      LogEntityEnum.SENSOR,
      updatedSensor._id.toString(),
      updatedBy,
      {
        assigned_org: assignmentDto.org_id,
        notes: assignmentDto.notes,
      },
      orgId
    );
    return updatedSensor.assignment;
  }

  async bulkUnAssignSensorFromOrg(node_ids: string[], updatedBy: string, orgId: string): Promise<null> {
    const promises = node_ids.map((node_id) => {
      return this.clearSensorAssignment(node_id, updatedBy).then(sensor => {
        return this.loggingService.createLog(
          LogActionEnum.UNASSIGN,
          LogEntityEnum.SENSOR,
          sensor._id.toString(),
          updatedBy,
          {
            assigned_org: "",
            notes: "",
          },
          orgId
        );
      });
    });

    await Promise.all(promises);
    return;
  }

  async unAssignSensorFromOrg(node_id: string, updatedBy: string, orgId: string): Promise<AssignmentDto> {
    const updatedSensor = await this.clearSensorAssignment(node_id, updatedBy);
    
    await this.loggingService.createLog(
      LogActionEnum.UNASSIGN,
      LogEntityEnum.SENSOR,
      updatedSensor._id.toString(),
      updatedBy,
      {
        assigned_org: "",
        notes: "",
      },
      orgId
    );
    return updatedSensor.assignment;
  }

  async updateSensorName(name: string, node_id: string, updatedBy: string, orgId: string): Promise<AssignmentDto> {
    const updatedSensor = await this.update(node_id, { 'assignment': { name, auth0_id: undefined, notes: undefined, org_id: undefined }}, updatedBy);
    await this.loggingService.createLog(
      LogActionEnum.UPDATE,
      LogEntityEnum.SENSOR,
      updatedSensor._id.toString(),
      updatedBy,
      {
        assigned_org: "",
        node_id: node_id,
        name: name,
      },
      orgId
    );
    
    return updatedSensor.assignment;
  }


  async findAll(page: number = 1, limit: number = 10, state?: string, fgNumber?: string, name?: string, auth0_id?: string, sort?: string, sortDirection?: string): Promise<{ sensors: Sensor[], total: number, page: number, totalPages: number }> {
    const cacheKey = `sensors:all:${page}:${limit}:${state || ''}:${fgNumber || ''}:${name || ''}:${auth0_id || ''}:${sort || ''}:${sortDirection || ''}`;
    
    // Check cache first
    if (process.env.ENABLE_CACHE === 'true') {
      const cached = await this.cacheManager.get(cacheKey);
      if (cached) {
        return cached as any;
      }
    }

    const skip = (page - 1) * limit;
    const filter: any = { };
    if (state) {
      filter['connectivity.committed_state'] = parseInt(state);
    }
    if (fgNumber) {
      filter['runtime.hostname'] = { $regex: fgNumber, $options: 'i' };
    }
    if(name) {
      filter['assignment.name'] = { $regex: name, $options: 'i' };
    }
    if(auth0_id) {
      filter['assignment.auth0_id'] = auth0_id;
    }

    // Build sort object
    const sortObj: any = {};
    if (sort) {
      sortObj[sort] = sortDirection === 'desc' ? -1 : 1;
    } else {
      // Default sort by TIME_STAMP descending
      sortObj['TIME_STAMP'] = -1;
    }

    const [sensors, total] = await Promise.all([
      this.sensorModel.aggregate([
        { $match: filter },
        { $lookup: {
          from: 'organizations',
          localField: 'assignment.org_id',
          foreignField: '_id',
          as: 'organization'
        }
      },
      {
        $project: {
          assignment: 1,
          node_id: 1,
          time_stamp: 1,
          connectivity: 1,
          organization: 1,
          location: 1,
          runtime: 1
        }
      },
        { $sort: sortObj },
        { $skip: skip },
        { $limit: limit },
      ]).exec(),
      this.sensorModel.countDocuments(filter).exec()
    ]);

    const result = {
      sensors,
      total,
      page,
      totalPages: Math.ceil(total / limit)
    };

    // Store in cache
    if (process.env.ENABLE_CACHE === 'true') {
      await this.cacheManager.set(cacheKey, result, SensorService.CACHE_TTL_SENSORS);
    }

    return result;
  }

  async findOne(id: string): Promise<Sensor> {
    const cacheKey = `sensor:${id}`;
    
    // Check cache first
    if (process.env.ENABLE_CACHE === 'true') {
      const cached = await this.cacheManager.get(cacheKey);
      if (cached) {
        return cached as Sensor;
      }
    }

    const sensor = await this.sensorModel.findOne({ 
      _id: id, 
      isDeleted: false 
    }).exec();

    if (!sensor) {
      throw new NotFoundException(`Sensor with ID ${id} not found`);
    }

    // Store in cache
    if (process.env.ENABLE_CACHE === 'true') {
      await this.cacheManager.set(cacheKey, sensor, SensorService.CACHE_TTL_SENSOR_DETAIL);
    }

    return sensor;
  }

  async findByNodeId(nodeId: string): Promise<Sensor> {
    const cacheKey = `sensor:node:${nodeId}`;
    
    // Check cache first
    if (process.env.ENABLE_CACHE === 'true') {
      const cached = await this.cacheManager.get(cacheKey);
      if (cached) {
        return cached as Sensor;
      }
    }

    const sensor = await this.sensorModel.findOne({ 
      NODE_ID: nodeId, 
      isDeleted: false 
    }).exec();

    if (!sensor) {
      throw new NotFoundException(`Sensor with NODE_ID ${nodeId} not found`);
    }

    // Store in cache
    if (process.env.ENABLE_CACHE === 'true') {
      await this.cacheManager.set(cacheKey, sensor, SensorService.CACHE_TTL_SENSOR_DETAIL);
    }

    return sensor;
  }

  async findByOrgId(orgId: string): Promise<Sensor[]> {
    const cacheKey = `sensors:org:${orgId}`;
    
    // Check cache first
    if (process.env.ENABLE_CACHE === 'true') {
      const cached = await this.cacheManager.get(cacheKey);
      if (cached) {
        return cached as Sensor[];
      }
    }

    const sensors = await this.sensorModel.find({
      'assignment.org_id': orgId,
      isDeleted: false
    }).sort({ TIME_STAMP: -1 }).exec();

    // Store in cache
    if (process.env.ENABLE_CACHE === 'true') {
      await this.cacheManager.set(cacheKey, sensors, SensorService.CACHE_TTL_SENSORS);
    }

    return sensors;
  }

  async update(node_id: string, sensorData: UpdateSensorDto, userId: string): Promise<Sensor> {
    // Check if sensor exists
    const sensor = await this.findByNodeIdAndThrowIfNotFound(node_id);

    // Update sensor
    const updatedSensor = await this.sensorModel.findByIdAndUpdate(
      sensor._id,
      {
        ...sensorData,
        assignment: {
          auth0_id: sensorData.assignment?.auth0_id || sensor.assignment?.auth0_id,
          org_id: sensorData.assignment?.org_id || sensor.assignment?.org_id,
          assigned_at: sensorData.assignment?.assigned_at || sensor.assignment?.assigned_at,
          notes: sensorData.assignment?.notes || sensor.assignment?.notes,
          name: sensorData.assignment?.name || sensor.assignment?.name
        }, 
        updated_by: new Types.ObjectId(userId),
        updated_at: new Date(),
      },
      { new: true }
    ).exec();

    // Clear cache
    if (process.env.ENABLE_CACHE === 'true') {
      await this.cacheManager.del(`sensor:${sensor._id}`);
      if (sensor.NODE_ID) {
        await this.cacheManager.del(`sensor:node:${sensor.NODE_ID}`);
      }
      // Clear list caches that might be affected
      await this.cacheManager.del(`sensors:all:${sensor.assignment?.org_id || 'all'}:1:10`);
      await this.cacheManager.del(`sensors:org:${sensor.assignment?.org_id}`);
    }

    return updatedSensor;
  }

  async clearSensorAssignment(node_id: string, userId: string): Promise<Sensor> {
    // Check if sensor exists
    const sensor = await this.findByNodeIdAndThrowIfNotFound(node_id);

    // Update sensor
    const updatedSensor = await this.sensorModel.findByIdAndUpdate(
      sensor._id,
      {
        assignment: {
          auth0_id: null,
          org_id: null,
          assigned_at: null,
          notes: "",
          name: ""
        }, 
        updated_by: new Types.ObjectId(userId),
        updated_at: new Date(),
      },
      { new: true }
    ).exec();

    // Clear cache
    if (process.env.ENABLE_CACHE === 'true') {
      await this.cacheManager.del(`sensor:${sensor._id}`);
      if (sensor.NODE_ID) {
        await this.cacheManager.del(`sensor:node:${sensor.NODE_ID}`);
      }
      // Clear list caches that might be affected
      await this.cacheManager.del(`sensors:all:${sensor.assignment?.org_id || 'all'}:1:10`);
      await this.cacheManager.del(`sensors:org:${sensor.assignment?.org_id}`);
    }

    return updatedSensor;
  }

  async delete(id: string, userId: string): Promise<{ message: string }> {
    const sensor = await this.sensorModel.findOne({ _id: id, isDeleted: false }).exec();

    if (!sensor) {
      throw new NotFoundException(`Sensor with ID ${id} not found`);
    }

    // Soft delete
    await this.sensorModel.findByIdAndUpdate(
      id,
      {
        isDeleted: true,
        isActive: false,
        deletedAt: new Date(),
        deletedBy: new Types.ObjectId(userId),
      }
    ).exec();

    // Clear cache
    if (process.env.ENABLE_CACHE === 'true') {
      await this.cacheManager.del(`sensor:${id}`);
      if (sensor.NODE_ID) {
        await this.cacheManager.del(`sensor:node:${sensor.NODE_ID}`);
      }
      // Clear list caches that might be affected
      await this.cacheManager.del(`sensors:all:${sensor.assignment?.org_id || 'all'}:1:10`);
      await this.cacheManager.del(`sensors:org:${sensor.assignment?.org_id}`);
    }

    return { message: 'Sensor deleted successfully' };
  }

  async getSensorStats(auth0_id?: string): Promise<SensorStatsDto> {
    const cacheKey = 'sensor:stats';

    // Check cache first
    if (process.env.ENABLE_CACHE === 'true') {
      const cached = await this.cacheManager.get(cacheKey);
      if (cached) {
        return cached as { totalSensors: number, onlineSensors: number, offlineSensors: number };
      }
    }
    const filter = {};

    if(auth0_id) {
      filter['assignment.auth0_id'] = auth0_id;
    }

    const stats = await this.sensorModel.aggregate([
      {
        $match: filter
      },
      {
        $group: {
          _id: null,
          totalSensors: { $sum: 1 },
          onlineSensors: {
            $sum: {
              $cond: [{ $eq: ['$connectivity.committed_state', 1] }, 1, 0]
            }
          },
          offlineSensors: {
            $sum: {
              $cond: [{ $ne: ['$connectivity.committed_state', 1] }, 1, 0]
            }
          }
        }
      },
      {
        $project: {
          _id: 0,
          totalSensors: 1,
          onlineSensors: 1,
          offlineSensors: 1
        }
      }
    ]).exec();

    const result = stats[0] || {
      totalSensors: 0,
      onlineSensors: 0,
      offlineSensors: 0
    };

    // Store in cache
    if (process.env.ENABLE_CACHE === 'true') {
      await this.cacheManager.set(cacheKey, result, SensorService.CACHE_TTL_SENSORS);
    }

    return result;
  }

  async findByNodeIdAndThrowIfNotFound(node_id: string): Promise<Sensor> {
    const sensor = await this.sensorModel.findOne({
      NODE_ID: node_id
    });

    if (!sensor) {
      throw new NotFoundException(`Sensor with NODE_ID ${node_id} not found`);
    }

    return sensor
  }

}
